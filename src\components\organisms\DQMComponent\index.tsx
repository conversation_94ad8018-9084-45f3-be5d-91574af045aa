import { styled } from "@linaria/react";
import { Dropdown, Segmented, Tag, Tooltip } from "antd";
import { DetailsContainer, GraphModal, MyTable, TestCard } from "../..";
import { KanbanListSkeleton } from "../../atoms";
import { useEffect, useMemo, useState } from "react";
import { AppstoreOutlined, BarsOutlined } from "@ant-design/icons";
import { useSearchParams } from "react-router-dom";
import { useTheme } from "../../../utils/useTheme";
import { GET_CHILDRENS, NODES_MENU_ITEMS } from "../../../constants";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { RootState } from "../../../store";
import { useQuery } from "react-query";
import { getAllNodesWithBody } from "../../../services/node";
import { ITemplates } from "../../../interfaces";
import { getDQMTestResults } from "../../../services";
import dayjs from "dayjs";
import { useHyperlinkActions } from "../../../utils/functions/customHooks";
import { getParentID } from "../../../utils";
import { Chart } from "primereact/chart";
import { withErrorBoundary } from "../../withErrorBoundary";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

const DQMComponentBase = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();

  const [listStyle, setListStyle] = useState("kanban");
  const [graphOpen, setGraphOpen] = useState(null);
  const [dqmData, setDqmData] = useState([]);
  const [isDetailsOpen, setDetailsOpen] = useState(null);

  const { handleHyperlinkAction } = useHyperlinkActions();

  const templatesData = useSelector(
    (rootState: RootState) => rootState.templatesStore.templates
  );
  const bottomNavigationMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  const authenticated = useSelector(
    (state: RootState) => state.auth.authenticated
  );

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
      }
    }
  };

  const COLUMNS = useMemo(() => {
    return [
      {
        headerName: "Name",
        field: "name",
        minWidth: 250,
        cellRenderer: ({ data }) => (
          <Dropdown
            menu={{
              items: NODES_MENU_ITEMS,
              onClick: (e) => handleNodeClick(e.key, data.id, data.name),
            }}
            trigger={["contextMenu"]}
          >
            <p
              className={`title-container`}
              onClick={async (e) => {
                e.stopPropagation();
                handleHyperlinkAction({
                  id: data.id,
                  inTrash: false,
                });
              }}
            >
              {data.name}
            </p>
          </Dropdown>
        ),
      },
      {
        headerName: "Alarm threshold",
        field: "alarmThreshold",
        minWidth: 200,
      },
      {
        headerName: "Warning threshold",
        field: "warningThreshold",
        minWidth: 210,
      },
      {
        headerName: "Status",
        field: "result",
        minWidth: 160,
        cellRenderer: ({ data }) => {
          const type =
            data?.testResults?.length === 0
              ? "new"
              : data?.testResults[0]?.type;
          return (
            <Tag color={type === "new" ? "default" : type}>
              {t(
                type === "new"
                  ? "Not started"
                  : type === "error"
                    ? "Error"
                    : type === "warning"
                      ? "Warning"
                      : "Success"
              )}
            </Tag>
          );
        },
      },

      {
        headerName: "Chart",
        field: "chart",
        minWidth: 130,
        width: 130,
        maxWidth: 130,
        cellRenderer: ({ data }) =>
          data?.testResults?.length === 0 ? (
            ""
          ) : (
            <ChartContainer onClick={() => setGraphOpen(data?.lineData)}>
              <LinesOverlay>
                <div className="line top" />
                <div className="line " />
                <div className="line " />
                <div className="line " />
                <div className="line " />
                <div className="line " />
                <div className="line " />
                <div className="line bottom" />
              </LinesOverlay>
              <Tooltip mouseEnterDelay={0.8} title="Preview Chart">
                <Chart
                  type="line"
                  options={{
                    layout: {
                      padding: 0,
                    },
                    responsive: false,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: { display: false },
                      tooltip: { enabled: false },
                    },
                    scales: {
                      x: { display: false },
                      y: { display: false },
                    },
                  }}
                  width="88"
                  height="25"
                  data={data?.miniChartLineData}
                />
              </Tooltip>
            </ChartContainer>
          ),
      },
    ];
  }, []);

  const { isLoading, data: childrenData } = useQuery(
    [GET_CHILDRENS, searchParams.get("nodeId")],
    () => getAllNodesWithBody(searchParams.get("nodeId")),
    {
      enabled: !!authenticated,
    }
  );

  useEffect(() => {
    const generateData = async () => {
      const dqmData = await generateDQMData(childrenData);
      setDqmData(dqmData);
    };

    if (childrenData) generateData();
  }, [childrenData]);

  const generateDQMData = async (childrensNode) => {
    const promises = childrensNode?.map(async (child) => {
      const childTemplate = templatesData[child?.templateId] as ITemplates;
      const WARNING_THRESHOLD_ID = childTemplate?.attributeTemplates?.find(
        (item) => item.attributeValueType === "WARNING_THRESHOLD"
      )?.id;

      const ALARM_THRESHOLD_ID = childTemplate?.attributeTemplates?.find(
        (item) => item.attributeValueType === "ALARM_THRESHOLD"
      )?.id;

      const alarmThresholdValue =
        (child?.body?.find((item) => item.id === ALARM_THRESHOLD_ID)?.value ||
          0) * 100;
      const alarmThresholdName = child?.body?.find(
        (item) => item.id === ALARM_THRESHOLD_ID
      )?.name;

      const warningThresholdName = child?.body?.find(
        (item) => item.id === WARNING_THRESHOLD_ID
      )?.name;

      const warningThresholdValue =
        (child?.body?.find((item) => item.id === WARNING_THRESHOLD_ID)?.value ||
          0) * 100;

      const testResultsResponse = await getDQMTestResults(
        child?.id?.toString()
      );
      const testResults = [];

      testResultsResponse?.forEach((data, index) => {
        if (index >= 10) return;
        const value = (data.errorRecords / data.sampleRecords) * 100;
        const type =
          value > alarmThresholdValue
            ? "error"
            : value > warningThresholdValue && value < alarmThresholdValue
              ? "warning"
              : "success";

        testResults.push({
          type: type,
          id: data.resultId,
          date: dayjs(data?.testTimestamp).format("YYYY-MM-DD HH:mm"),
        });
      });

      return {
        key: child?.id,
        id: child?.id,
        name: child.name,
        testResults: testResults,
        alarmThreshold: alarmThresholdValue + "%",
        warningThreshold: warningThresholdValue + "%",
        series: [
          {
            name: "series-1",
            data: testResultsResponse?.map((chart) =>
              ((chart.errorRecords / chart.sampleRecords) * 100).toFixed(2)
            ),
          },
        ],

        miniChartLineData: {
          labels: testResultsResponse?.map((chart) =>
            dayjs(chart.testTimestamp).format("YYYY-MM-DD HH:mm")
          ),
          datasets: [
            {
              label: "[Lbr / Lpr] (%)",
              data: testResultsResponse?.map((chart) =>
                ((chart.errorRecords / chart.sampleRecords) * 100).toFixed(2)
              ),
              fill: false,
              pointRadius: 0,
              borderColor: "#4377a2", // Line color
              tension: 0.4, // Line smoothness
              borderWidth: 2,
            },
          ],
        },

        lineData: {
          labels: testResultsResponse?.map((chart) =>
            dayjs(chart.testTimestamp).format("YYYY-MM-DD HH:mm")
          ),
          datasets: [
            {
              label: alarmThresholdName,
              data: testResultsResponse?.map(() => alarmThresholdValue),
              fill: false,
              pointRadius: 0,
              borderColor: "red", // Line color
              tension: 0.4, // Line smoothness
              borderWidth: 2,
            },

            {
              label: warningThresholdName,
              data: testResultsResponse?.map(() => warningThresholdValue),
              fill: false,
              pointRadius: 0,
              borderColor: "orange", // Line color
              tension: 0.4, // Line smoothness
              borderWidth: 2,
            },

            {
              label: "[Lbr / Lpr] (%)",
              data: testResultsResponse?.map((chart) =>
                ((chart.errorRecords / chart.sampleRecords) * 100).toFixed(2)
              ),
              fill: false,
              pointRadius: 4,
              borderColor: "#4377a2", // Line color
              tension: 0.4, // Line smoothness
              borderWidth: 2,
            },
          ],
        },
      };
    });

    return Promise.all(promises);
  };

  // const hasChildrens = () => {
  //   let hasChildren = false;
  //   dqmData?.forEach((data) => {
  //     if (data.children.length > 0) {
  //       hasChildren = true;
  //     }
  //   });
  //   return hasChildren;
  // };

  return (
    <Wrapper>
      <Segmented
        disabled={bottomNavigationMask}
        onChange={(value: string) => setListStyle(value)}
        options={[
          {
            value: "kanban",
            icon: <AppstoreOutlined />,
          },
          {
            value: "list",
            icon: <BarsOutlined />,
          },
        ]}
      />
      {isLoading ? (
        <KanbanListSkeleton />
      ) : (
        <Content isTable={listStyle === "list"}>
          {bottomNavigationMask && <div className="mask" />}
          {listStyle === "kanban" ? (
            <Grid style={{ padding: 20 }}>
              {dqmData?.map((item) => (
                <TestCard key={item.key} {...item} id={item.key} />
              ))}
            </Grid>
          ) : (
            <TableWrapper theme={theme}>
              <MyTable
                height={"calc(100vh - 220px)"}
                columns={COLUMNS}
                data={dqmData}
                noSelect
                noHeader
                excelFileName="DQM"
              />
            </TableWrapper>
          )}
        </Content>
      )}
      {!!graphOpen && (
        <GraphModal
          onClose={() => setGraphOpen(null)}
          lineData={graphOpen}
          isOpen={!!graphOpen}
        />
      )}

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Wrapper>
  );
};

export const DQMComponent = withErrorBoundary(
  DQMComponentBase,
  "error.generic"
);

const ChartContainer = styled.div`

  position: relative;
  position: relative;
  cursor: pointer;
  & > div {
    min-height: auto !important;
  }
`;

const LinesOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .line {
    width: 100%;
    height: 1px;
    background-color: #ebebeb;
    &.top,
    &.bottom {
      visibility: hidden;
    }
  }
`;
const TableWrapper = styled.div<{ theme: any }>`
  overflow: auto hidden;
  padding: 20px 10px 10px;

  & .table-wrapper {
    height: 100%;
  }

  & .ag-cell-wrapper,
  & .ag-cell-value {
   height: 100%;
   display: flex;
   flex-direction: column;
   justify-content: center;
   padding-top: 0px;
   padding-bottom: 0px;
  }
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
  min-width: 300px;

  @media (max-width: 1025px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (max-width: 895px) {
    grid-template-columns: 1fr;
  }

  & .error {
    background-color: #b61c07f2;
    border: 1px solid #b61c07f2;
  }

  & .new-test {
    background-color: #c1c1c1f1;
    border: 1px solid #c1c1c1f1;
  }

  & .success {
    background-color: #31a354;
    border: 1px solid #31a354;
  }

  & .inactive {
    background-color: #f0efeff2;
    border: 1px solid #ccccccf2;
  }

  & .warning {
    background-color: #ff9f40;
    border: 1px solid #ff9f40;
  }
`;

const Content = styled.div<{ isTable: boolean }>`
  flex: 1;
  display: flex;
  position: relative;
  & .mask {
    height: 100%;
    width: 100%;
  }
  overflow-y: ${({ isTable }) => (isTable ? "hidden" : "auto")};
  flex-direction: column;
  /* padding: 10px; */

  & .ant-tabs-nav {
    margin-bottom: 0px;
  }

  & .ant-segmented {
    margin-top: 4px;
    margin-bottom: 4px;
    display: flex;
    width: fit-content;
  }

  & .ant-segmented-item-selected {
    color: #4277a2;
  }
  & .ant-tabs-nav-list > div:first-child {
    margin-left: 0px !important;
  }
  & .ant-tabs-tab {
    padding-top: 0px;
    font-size: 13px;
    margin-left: 16px !important;
  }

  & .ant-tabs-tab-btn {
    color: #858080;
  }
`;

const Wrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;

  & .ant-segmented {
    width: fit-content;
    margin-left: auto;
  }
`;
