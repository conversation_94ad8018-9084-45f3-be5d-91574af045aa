/// <reference types="vite-plugin-svgr/client" />
import { styled } from "@linaria/react";
import { AttributeItem, AttributeListSkeleton } from "../../atoms";
import { useLocation, useParams, useSearchParams } from "react-router-dom";
import { useQuery } from "react-query";
import { getNodeDetails, getParentNameDetails } from "../../../services/node";
import { Empty } from "antd";
import { memo, useCallback, useEffect, useState } from "react";
import { PlusOutlined } from "@ant-design/icons";
import {
  ATTRIBUTES_WITH_NO_MANDATORY,
  DQM_GROUPING_NODES,
  GET_NODE_ATTRIBUTES_DETAILS,
  METAATTRIBUTE_ID_ATRIBUTETYPE,
  METAATTRIBUTE_ID_DEFAULT_VALUES,
  METAATTRIBUTE_ID_DISPLAY_OPTIONS,
  METAATTRIBUTE_ID_DROPDOWNITEMS,
  METAATTRIBUTE_ID_ISMANDATORY,
  METAATTRIBUTE_ID_PARENT1,
  METAATTRIBUTE_ID_PARENT1_PREVIEW,
  METAATTRIBUTE_ID_PARENT2,
  METAATTRIBUTE_ID_PARENT2_PREVIEW,
  METAATTRIBUTE_ID_REGEX,
  OBJECT_TEMPLATE_ID,
  TEMP_GROUPING_NODE_ID,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
} from "../../../constants";
import { IAttributes, INodeDetails, ITreeData } from "../../../interfaces";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setDisplaySaveButton, setMask } from "../../../store/features/sidebar";
import { RootState } from "../../../store";
import { searchRecursivelyByKey } from "../../../utils/functions/recursives";
import { deepClone, getAttributeTitleWidth } from "../../../utils";
import { I_MULTIPLICITY } from "../../../interfaces/IAttributeTypes";
import { Splitter, SplitterPanel } from "primereact/splitter";
import { DQMComponent } from "../DQMComponent";
import isEqual from "lodash.isequal";
import _ from "lodash";
import { usePermissions, useFlags } from "../../../utils/functions/customHooks";
import { AddNewAttribute } from "../../molecules";
import { withErrorBoundary } from "../../withErrorBoundary";
import { BitFlag } from "../../../constants/enums";

interface Props {
  attributes: any[];
  setAttributes: any;
  metamodel?: boolean;
  setEditingAttribute: any;
  editingAttribute: any;
  setMandatoryAttributes: any;
  mandatoryAttributes: any;
  setSpecialAttribute?: any;
  updateAllowedChildrens: (arg0: number[]) => void;
  treeData: ITreeData[];
  initialAttributes: any;
  setInitialAttributes: any;
}

export const withRegexAttributes = ["editor", "number", "text", "textarea"];

const WorkspaceContainerComponent = memo(
  ({
    attributes,
    setAttributes,
    initialAttributes,
    setInitialAttributes,
    metamodel,
    editingAttribute,
    setEditingAttribute,
    setMandatoryAttributes,
    mandatoryAttributes,
    setSpecialAttribute,
    updateAllowedChildrens,
    treeData,
  }: Props) => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const dispatch = useDispatch();
    const location = useLocation();
    const params = useParams();

    const { getPermissions } = usePermissions();
    const { getFlags } = useFlags();

    // Detect authors context
    const isAuthorsContext = location.pathname.includes("authors");

    const [loading, setLoading] = useState(true);
    const [permissions, setPermissions] = useState([]);

    const [newAttributes, setNewAttributes] = useState([]);
    const [widthCalculated, setWidthCalculated] = useState(false);

    const templatesData = useSelector(
      (state: RootState) => state.templatesStore.templates
    );
    const globalPermissions = useSelector(
      (state: RootState) => state.auth.globalPermissions
    );
    const workingVersionActive = useSelector(
      (state: RootState) => state.mask.workingVersion
    );
    const movingMask = useSelector((state: RootState) => state.mask.movingMask);
    const attributeMask = useSelector(
      (state: RootState) => state.sidebar.attributeMask
    );
    const { trashCollapsed } = useSelector((state: RootState) => state.trash);
    const selected = useSelector((state: RootState) => state.sidebar.selected);
    // const displaySaveButton = useSelector(
    //   (state: RootState) => state.sidebar.displaySaveButton
    // );
    const [isDQM, setIsDQM] = useState(false);

    const getNonHiddenAttributes = (attrs) => {
      return []
        .concat(attrs)
        .filter(
          (attr) =>
            !getFlags(attr.bitFlag).includes(BitFlag.HIDDEN) ||
            (+searchParams.get("nodeId") === TEMP_GROUPING_NODE_ID &&
              attr.type !== "allowedChildren")
        );
    };

    const generateValue = (id, type, value, defaultValue) => {
      if (type === I_MULTIPLICITY) {
        return {
          text1: value?.split("..")[0],
          text2: value?.split("..")[1],
        };
      } else if (id === METAATTRIBUTE_ID_ISMANDATORY) return false;
      else if (type === "switch") {
        return value || defaultValue || false;
      }
      return defaultValue || null;
    };

    useEffect(() => {
      setLoading(true);
    }, [params?.nodeId]);

    useEffect(() => {
      if (!searchParams.get("nodeId")) setLoading(false);
    }, [searchParams.get("nodeId"), params?.nodeId]);

    useEffect(() => {
      // for draft nodes
      if (searchParams.get("draft")) {
        setIsDQM(false);
        setLoading(false);
        setInitialAttributes([]);
        setAttributes([]);
        setNewAttributes([]);
        setPermissions(globalPermissions);

        if (!templatesData || !searchParams.get("template")) {
          return;
        }
        const selectedTemplate =
          templatesData[Number(searchParams.get("template"))];

        if (selectedTemplate) {
          const mandatoryAttributes = [] as number[];
          const attributeTemplates = [];

          selectedTemplate?.attributeTemplates?.forEach(
            (attribute: IAttributes, index: number) => {
              const newAttribute = { ...attribute };
              if (isMandatoryMultiplicity(attribute)) {
                mandatoryAttributes.push(attribute.id);
              }
              newAttribute.id = attribute.id || index + 1;
              newAttribute.mandatory = isMandatoryMultiplicity(attribute);
              newAttribute.value = generateValue(
                attribute.id,
                attribute.type,
                attribute.value,
                attribute?.defaultValue?.value
              );
              newAttribute["touched"] = false;
              newAttribute["draft"] = true;
              // Preserve the bitFlag from the attribute template
              newAttribute.bitFlag = attribute.bitFlag;
              attributeTemplates.push(newAttribute);
            }
          );
          setMandatoryAttributes([...mandatoryAttributes]);
          setAttributes([...attributeTemplates]);
        }
      }
    }, [searchParams.get("draft")]);

    const isMandatoryMultiplicity = (attribute) => {
      // In authors context, disable mandatory flags
      if (isAuthorsContext) {
        return false;
      }

      if (!ATTRIBUTES_WITH_NO_MANDATORY.includes(attribute.type)) {
        return attribute?.mandatory;
      }

      if (attribute.type === "relation") {
        const multiplicity = attribute.multiplicity?.split("..")[0];
        if (!["0", "n"].includes(multiplicity)) {
          return true;
        }
      } else if (
        attribute.type === "compound" ||
        attribute.type === "compoundSimple" ||
        attribute.type === "roleMatrix"
      ) {
        const multiplicityOfList1 = attribute.multiplicityList1?.split("..")[0];
        const multiplicityOfList2 = attribute.multiplicityList2?.split("..")[0];
        if (!["0", "n"].includes(multiplicityOfList1)) {
          return true;
        }
        if (!["0", "n"].includes(multiplicityOfList2)) {
          return true;
        }
      }
      return false;
    };

    const { isLoading, isError, data, isFetching } = useQuery<
      INodeDetails,
      any
    >(
      [GET_NODE_ATTRIBUTES_DETAILS, searchParams.get("nodeId")],
      () => getNodeDetails(searchParams.get("nodeId")),
      {
        onSuccess: () => {
          setLoading(false);
        },
        retry: 1,
        enabled:
          !!searchParams.get("nodeId") &&
          !!templatesData &&
          !searchParams.get("draft"),
      }
    );

    useEffect(() => {
      if (!data) {
        return;
      }

      setPermissions(getPermissions(data?.permissionsId));
      loadAttributes();
    }, [data, isFetching]);

    const loadAttributes = async () => {
      setNewAttributes([]);
      setAttributes([]);

      const attributes = [];
      const mandatoryAttributes = [];
      const selectedTemplateAttributes =
        templatesData[data.templateId]?.attributeTemplates || [];
      setIsDQM(data?.templateId === DQM_GROUPING_NODES);

      if (location.pathname.includes("metamodel")) {
        let defaultValue = null;
        const attributeType = data?.body?.find(
          (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
        )?.value;
        const defaultValueIndex = data.body?.findIndex(
          (attr) => attr.id === METAATTRIBUTE_ID_DEFAULT_VALUES
        );
        if (
          attributeType &&
          Object.keys(attributeType)[0] === "multipleSelect" &&
          defaultValueIndex !== -1
        )
          defaultValue = Object.keys(data.body[defaultValueIndex]?.value)[0];
        data.body
          ?.filter((attr) => !getFlags(attr.bitFlag).includes(BitFlag.HIDDEN))
          .forEach((attribute: IAttributes) => {
            // If the attribute is a blackbox or allowedChildren,
            // for template id -235 which is folder type, we need to skip the node processing
            if (
              attribute?.type === "blackbox" ||
              (attribute?.type === "allowedChildren" &&
                +data.id === TEMP_GROUPING_NODE_ID)
            ) {
              return;
            }
            const index = selectedTemplateAttributes?.findIndex(
              (item) => item.id == attribute.id
            );

            if (
              selectedTemplateAttributes[index]?.mandatory &&
              !isAuthorsContext
            ) {
              mandatoryAttributes.push(attribute.id);
            }

            attributes.push({
              ...attribute,
              order: index,
              mandatory:
                selectedTemplateAttributes[index]?.mandatory &&
                !isAuthorsContext,
              help: selectedTemplateAttributes[index]?.help,
              regex: selectedTemplateAttributes[index]?.regex,
              items: selectedTemplateAttributes[index]?.items,
              value:
                attribute.type === "multiplicity"
                  ? {
                    text1: attribute?.value?.split("..")[0],
                    text2: attribute?.value?.split("..")[1],
                  }
                  : attribute.type === "switch"
                    ? attribute?.value || false
                    : attribute?.type === "dropdownItems"
                      ? Object.keys(attribute?.value || {})?.map((key) => {
                        return {
                          key: key,
                          name: attribute.value[key],
                          default: defaultValue === key || false,
                        };
                      })
                      : attribute?.value,
            });
          });

        const attributeTypeAttribute = data.body?.find(
          (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
        );
        if (attributeTypeAttribute) {
          const attributeType = Object.keys(attributeTypeAttribute?.value)[0];
          if (attributeType === "compound") {
            const parent1Index = attributes.findIndex(
              (attr) => attr.id === METAATTRIBUTE_ID_PARENT1
            );
            const parent2Index = attributes.findIndex(
              (attr) => attr.id === METAATTRIBUTE_ID_PARENT2
            );
            const parentDetails = await getParentNameDetails(
              [
                attributes[parent1Index]?.value,
                attributes[parent2Index]?.value,
              ]?.join()
            );
            const preview1Index = attributes.findIndex(
              (attr) => attr.id === METAATTRIBUTE_ID_PARENT1_PREVIEW
            );
            const preview2Index = attributes.findIndex(
              (attr) => attr.id === METAATTRIBUTE_ID_PARENT2_PREVIEW
            );

            attributes[preview1Index].value = parentDetails?.find(
              (attr) => attr.id == attributes[parent1Index]?.value
            )?.name;
            attributes[preview2Index].value = parentDetails?.find(
              (attr) => attr.id == attributes[parent2Index]?.value
            )?.name;

            const item = attributes.splice(preview1Index, 1)[0];
            attributes.splice(parent1Index + 1, 0, item);
            const item1 = attributes.splice(preview2Index, 1)[0];
            attributes.splice(parent2Index + 2, 0, item1);
          }
        }
      } else {
        if (
          getNonHiddenAttributes(selectedTemplateAttributes).length === 0 &&
          !location.pathname.includes("metamodel")
        ) {
          setLoading(false);
          return;
        }

        getNonHiddenAttributes(selectedTemplateAttributes)?.forEach(
          (attribute: IAttributes) => {
            const attributeValue = data?.body?.find(
              (item) => item.id == attribute.id
            );
            if (attributeValue) {
              if (isMandatoryMultiplicity(attribute)) {
                mandatoryAttributes.push(attribute.id);
              }

              attributes.push({
                ...attributeValue,
                ...attribute,
                value:
                  attribute.type === "multiplicity"
                    ? {
                      text1: attributeValue?.value?.split("..")[0],
                      text2: attributeValue?.value?.split("..")[1],
                    }
                    : attribute.type === "switch"
                      ? attributeValue?.value || false
                      : attribute.type === "relation" ||
                        attribute.type === "lifecycle"
                        ? (attributeValue?.value || []).map((item) => {
                          const hasAttributes =
                            templatesData[item.templateId]?.attributeTemplates
                              ?.length > 0;
                          return {
                            ...item,
                            templateHasAttributes: hasAttributes,
                          };
                        })
                        : attributeValue?.value,
                mandatory: isMandatoryMultiplicity(attribute),
                // Preserve the bitFlag from the attribute template
                bitFlag: attribute.bitFlag,
              });
            }
          }
        );
      }

      setMandatoryAttributes([...mandatoryAttributes]);
      setAttributes([...attributes]);
      const cleanAttributes = getNonHiddenAttributes(deepClone(attributes)).map(
        (attr) => {
          if (attr.type === "compound") {
            if (Array.isArray(attr.value)) {
              return {
                ...attr,
                value: attr.value.map((item) => {
                  if (Array.isArray(item.value)) {
                    return {
                      ...item,
                      value: item.value.map((subItem) => {
                        if (subItem.value === null) {
                          // eslint-disable-next-line @typescript-eslint/no-unused-vars
                          const { value, ...rest } = subItem; // Remove `value: null`
                          return rest;
                        }
                        return subItem;
                      }),
                    };
                  }
                  return item;
                }),
              };
            }
            return { ...attr, value: [] };
          }

          return attr;
        }
      );

      setInitialAttributes(cleanAttributes);
      setLoading(false);
    };

    const calculateWidth = () => {
      const titles = document.querySelectorAll(".attribute-title") as any;

      titles.forEach((title) => {
        title.style.width = `fit-content`;
      });
      const maxTitleWidth = getAttributeTitleWidth(".attribute-title");
      titles.forEach((title) => {
        title.style.width = `${maxTitleWidth}px`;
      });
      setWidthCalculated(true);
    };

    useEffect(() => {
      calculateWidth();

      setTimeout(() => {
        calculateWidth();
      }, 300);
    }, [attributes, trashCollapsed, newAttributes, editingAttribute]);

    useEffect(() => {
      if (!searchParams.get("draft") && !metamodel) {
        const selectedNode = searchRecursivelyByKey(
          treeData,
          searchParams.get("nodeId"),
          "key"
        );

        if (selectedNode && templatesData) {
          const selectedTemplate =
            templatesData[Number(selectedNode.templateId)];
          const allAttributeTemplates = selectedTemplate?.attributeTemplates;

          if (selectedTemplate) {
            if (allAttributeTemplates.length > attributes.length) {
              const newAttributes = [];
              const allAttributes = [...allAttributeTemplates];
              allAttributes
                .filter(
                  (attr) => !getFlags(attr.bitFlag).includes(BitFlag.HIDDEN)
                )
                .forEach((newAttribute, index) => {
                  const currentIndex = attributes.findIndex(
                    (attributes) => attributes.id == newAttribute.id
                  );
                  if (currentIndex === -1) {
                    newAttributes.push({
                      ...newAttribute,
                      label: newAttribute.name,
                      value: newAttribute.id,
                      order: index,
                    });
                  }
                });
              setNewAttributes([...newAttributes]);
            } else {
              setNewAttributes([]);
            }
          }
        }
      }
    }, [attributes, treeData]);

    useEffect(() => {
      // if (!searchParams.get("draft")) setLoading(true);

      setEditingAttribute(null);
      dispatch(setDisplaySaveButton(false));
      const timer = setTimeout(() => {
        if (!searchParams.get("nodeId")) {
          setAttributes([]);
          setLoading(false);
        }
      }, 500);
      return () => clearTimeout(timer);
    }, [searchParams.get("nodeId"), searchParams.get("draft")]);

    const handleEdit = (id, value, type, attrType) => {
      if (["password", "relation"].includes(attrType) && setSpecialAttribute) {
        setSpecialAttribute(true);
      }

      // if (!displaySaveButton) {
      //   dispatch(setDisplaySaveButton(true));
      //   dispatch(setMask(true));
      // }

      let allAttributes = [...attributes];

      if (id === METAATTRIBUTE_ID_ATRIBUTETYPE) {
        const selectedAttributeType = Object.keys(value)[0];

        const isDropdownItemsPresent =
          attributes.findIndex((item) => item.type === "dropdownItems") !== -1;

        const isRegexPresent =
          attributes.findIndex((item) => item.type === "regex") !== -1;

        //for regex
        if (
          !isRegexPresent &&
          withRegexAttributes.includes(selectedAttributeType)
        ) {
          const indexOfAttributeTemplate = attributes.findIndex(
            (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
          );
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_REGEX,
            mandatory: false,
            name: "Regex",
            type: "regex",
            value: null,
          });
        } else if (
          isRegexPresent &&
          !withRegexAttributes.includes(selectedAttributeType)
        ) {
          allAttributes = allAttributes.filter((item) => item.type !== "regex");
        }

        // for dropdown items
        if (
          !isDropdownItemsPresent &&
          ["multipleSelect", "select"].includes(selectedAttributeType)
        ) {
          const indexOfAttributeTemplate = attributes.findIndex(
            (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
          );
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_DROPDOWNITEMS,
            mandatory: true,
            name: "Dropdown Items",
            type: "dropdownItems",
            value: null,
          });
          setMandatoryAttributes([
            ...mandatoryAttributes,
            METAATTRIBUTE_ID_DROPDOWNITEMS,
          ]);
        } else if (
          isDropdownItemsPresent &&
          !["multipleSelect", "select"].includes(selectedAttributeType)
        ) {
          allAttributes = allAttributes.filter(
            (item) => item.type !== "dropdownItems"
          );
        }
      }

      if (type === "dropdownItems") {
        let valueIndex;
        // for single select
        valueIndex = allAttributes.findIndex((item) => item.type === "select");
        const findIndex = value?.find(
          (item) => item.value === allAttributes[valueIndex]?.value
        );
        if (!findIndex || findIndex === -1) {
          allAttributes[valueIndex].value = "";
        }

        // for multi-select

        valueIndex = allAttributes.findIndex(
          (item) => item.type === "multipleSelect"
        );
        const multipleItems = [];
        allAttributes[valueIndex]?.value?.forEach((multiSelect) => {
          const findIndex = value?.find((item) => item.value === multiSelect);
          if (findIndex) {
            multipleItems.push(multiSelect);
          }
        });
        allAttributes[valueIndex].value = multipleItems;
      }

      const findIndex = allAttributes.findIndex((item) => item.id === id);

      // Only process array values for relation-type attributes
      if (
        Array.isArray(value) &&
        ["relation", "lifecycle"].includes(attrType)
      ) {
        value = value.map((item) => {
          const hasAttributes =
            templatesData[item.templateId]?.attributeTemplates?.length > 0;
          return {
            ...item,
            templateHasAttributes: hasAttributes,
          };
        });
      }

      allAttributes[findIndex].value = value;
      setAttributes([...allAttributes]);

      const hasChanges = allAttributes.some((attr, index) => {
        if (attr.type === "relation" || attr.type === "lifecycle") {
          return !isEqual(
            _.sortBy(attr.value || [], "id"),
            _.sortBy(initialAttributes[index]?.value || [], "id")
          );
        }

        if (attr.type === "number") {
          return !isEqual(
            Number(attr.value),
            Number(initialAttributes[index]?.value)
          );
        }
        return !isEqual(attr.value, initialAttributes[index]?.value);
      });

      dispatch(setDisplaySaveButton(hasChanges));
      dispatch(setMask(hasChanges));

      if (type === "allowedChildren") {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        updateAllowedChildrens && updateAllowedChildrens(value);
      }
    };

    const getSelectItems = (id, items, type, attributeId) => {
      if (id === METAATTRIBUTE_ID_DISPLAY_OPTIONS) {
        return [
          { value: "hierarchy", label: t("Hierarchy") },
          { value: "tabular", label: t("Tabular") },
        ];
      }

      if (type === "lifecycle") {
        return attributeId;
      }
      if (items && type === "relation") {
        return items;
      }
      if (id === METAATTRIBUTE_ID_ATRIBUTETYPE) {
        if (templatesData) {
          const attributeTemplate =
            templatesData[TEMPLATES_ATTRIBUTE_TEMPLATE_ID]
              ?.attributeTemplates || [];

          if (attributeTemplate && attributeTemplate.length > 0) {
            const items = attributeTemplate.find(
              (item) => item.id === METAATTRIBUTE_ID_ATRIBUTETYPE
            )?.items;
            if (items && Object.keys(items).length > 0) {
              const dropdownItems = [];
              Object.keys(items).map((item) => {
                dropdownItems.push({ label: items[item], value: item });
              });

              return dropdownItems;
            }
          }
        }
      } else if (items) {
        const dropdownItems = [];
        Object.keys(items).map((item) => {
          dropdownItems.push({ label: items[item], value: item });
        });
        return dropdownItems;
      }
      return [];
    };

    const handleAddNewAttributes = (selectedAttributes: number[]) => {
      dispatch(setMask(true));
      const toBeAddedAttributes = [];
      const mandatory = [...mandatoryAttributes];
      selectedAttributes?.forEach((attributeId) => {
        const newAttribute = newAttributes.find(
          (item: any) => item.value === attributeId
        );

        if (newAttribute) {
          if (
            newAttribute.bitFlag !== undefined &&
            getFlags(newAttribute.bitFlag).includes("EDIT_VALUE_OFF")
          ) {
            return;
          }

          if (isMandatoryMultiplicity(newAttribute)) {
            mandatory.push(newAttribute.id);
          }
          newAttribute.id = attributeId;
          delete newAttribute.attributeId;
          delete newAttribute.label;
          newAttribute.value = generateValue(
            newAttribute.id,
            newAttribute.type,
            newAttribute.value,
            newAttribute?.defaultValue?.value
          );
          newAttribute.mandatory = isMandatoryMultiplicity(newAttribute);
          toBeAddedAttributes.push(newAttribute);
        }
      });

      const allAttributeTemplates =
        templatesData[Number(selected.info[0]?.templateId)]
          ?.attributeTemplates || [];

      const allAttributes = [];
      const newAttributesTemp = [...attributes, ...toBeAddedAttributes];

      allAttributeTemplates?.forEach((attr) => {
        const selected = getNonHiddenAttributes(newAttributesTemp).find(
          (att) => att.id === attr.id
        );
        if (selected) {
          allAttributes.push(selected);
        }
      });

      setAttributes(getNonHiddenAttributes(allAttributes));
      setMandatoryAttributes([...mandatory]);
    };

    const escFunction = useCallback((event) => {
      if (event.key === "Escape") {
        setEditingAttribute(null);
      }
    }, []);

    useEffect(() => {
      document.addEventListener("keydown", escFunction, false);

      return () => {
        document.removeEventListener("keydown", escFunction, false);
      };
    }, [escFunction]);

    const isFolderTemplateAttribute =
      +searchParams.get("nodeId") === TEMP_GROUPING_NODE_ID;

    const content = (
      <>
        {getNonHiddenAttributes(attributes).length === 0 &&
          getNonHiddenAttributes(newAttributes).length === 0 ? (
          <EmptyContainer>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t("No attributes")}
            />
          </EmptyContainer>
        ) : (
          <div className="attributes-wrapper" id="workspace-area">
            {getNonHiddenAttributes(attributes).map((item) => {
              return (
                <AttributeItem
                  key={item.id}
                  {...item}
                  onEdit={(value) =>
                    handleEdit(item.id, value, item.name, item.type)
                  }
                  title={item.name}
                  dropdownItems={getSelectItems(
                    item?.id,
                    item?.allowedLinksValues || item?.items,
                    item?.type,
                    item?.id
                  )}
                  isEditing={editingAttribute === item.id}
                  onEditClick={(editingAttribute) => {
                    if (editingAttribute) {
                      const allAttributes = [
                        ...getNonHiddenAttributes(attributes),
                      ];
                      const selectedAttributeIndex = allAttributes?.findIndex(
                        (attr) => attr.id === editingAttribute
                      );

                      const selectedAttribute =
                        allAttributes[selectedAttributeIndex];

                      if (
                        selectedAttribute?.draft &&
                        selectedAttribute?.defaultValue?.value &&
                        !selectedAttribute?.touched
                      ) {
                        selectedAttribute.value = null;
                      }
                      selectedAttribute.touched = true;
                      setAttributes([...allAttributes]);
                    }
                    setEditingAttribute(editingAttribute);
                  }}
                  titleClassName={"attribute-title"}
                  readOnly={
                    isAuthorsContext ||
                    !permissions.includes("EDIT") ||
                    data?.templateId === TEMPLATES_ATTRIBUTE_TEMPLATE_ID ||
                    data?.templateId === OBJECT_TEMPLATE_ID ||
                    (item.bitFlag !== undefined &&
                      getFlags(item.bitFlag).includes("EDIT_VALUE_OFF"))
                  }
                />
              );
            })}
            {getNonHiddenAttributes(newAttributes).length > 0 &&
              !searchParams.get("draft") &&
              searchParams.get("nodeId") &&
              !isFolderTemplateAttribute &&
              permissions.includes("EDIT") && (
                <AddNewAttributes>
                  <h6
                    onClick={() => {
                      setEditingAttribute("add-attribute");
                    }}
                  >
                    {t("New attributes exists")}
                  </h6>

                  {editingAttribute === "add-attribute" ? (
                    <AddNewAttribute
                      data={getNonHiddenAttributes(newAttributes)}
                      open={editingAttribute === "add-attribute"}
                      onClose={() => {
                        setEditingAttribute(null);
                      }}
                      onAddAttributes={handleAddNewAttributes}
                    />
                  ) : (
                    <div
                      className="content"
                      onClick={() => {
                        setEditingAttribute("add-attribute");
                      }}
                    >
                      <PlusOutlined />
                    </div>
                  )}
                </AddNewAttributes>
              )}
          </div>
        )}
      </>
    );

    return (
      <Wrapper onClick={(e) => e.stopPropagation()}>
        {(workingVersionActive || movingMask || attributeMask) && (
          <Mask className="mask" />
        )}

        {isError ? (
          <EmptyContainer>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t("Error occurred in fetching node details!")}
            />
          </EmptyContainer>
        ) : loading ||
          isLoading ||
          (getNonHiddenAttributes(attributes)?.length > 0 &&
            !widthCalculated) ? (
          <AttributeListSkeleton />
        ) : isDQM && !searchParams.get("draft") ? (
          getNonHiddenAttributes(attributes).length === 0 &&
            getNonHiddenAttributes(newAttributes).length === 0 ? (
            <DQMComponent />
          ) : (
            <Splitter layout="vertical">
              <SplitterPanel>{content}</SplitterPanel>
              <SplitterPanel>
                <DQMComponent />
              </SplitterPanel>
            </Splitter>
          )
        ) : (
          content
        )}
      </Wrapper>
    );
  }
);

export const WorkspaceContainer = withErrorBoundary(
  WorkspaceContainerComponent,
  "error.generic"
);

const AddNewAttributes = styled.div`
  border: 0.5px dashed #09437561;
  display: flex;
  width: 100%;

  border-radius: 4px;
  margin-top: 10px;

  &:hover {
    opacity: 0.8;
  }
  & .content {
    padding: 6px;
    font-size: 14px;
    flex: 1;
    cursor: pointer;
    color: var(--color-text);
  }
  & h6 {
    font-size: 13px;
    cursor: pointer;
    font-weight: 400;
    padding: 8px 10px;
    border-right: 1px solid #eaeaea;
    max-width: 80%;
    background-color: var(--color-light);
    color: var(--color-text);
  }
`;
const EmptyContainer = styled.div`
  margin-top: 15%;
`;

const Wrapper = styled.div`
  padding: 10px;
  column-gap: 10px;
  overflow: auto;
  flex: 1;
  min-width: 300px;

  & .p-splitter {
    border: none;
  }

  & .p-splitter-gutter {
    background: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: "⋯";
      font-size: 30px;
      color: #e0dbdb;
      line-height: 1;
    }
  }

  & .p-splitter-gutter-handle {
    display: none;
  }
`;

const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 1;
  margin: -10px;
`;
